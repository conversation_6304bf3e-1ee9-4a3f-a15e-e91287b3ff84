/* eslint-disable tailwindcss/no-custom-classname */
import React, { useState, useEffect } from 'react';
import { loginStore, type Project } from '@extension/storage';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Loa<PERSON>, FiRefresh<PERSON>w, <PERSON>Check } from 'react-icons/fi';

interface ProjectSelectorProps {
  isDarkMode?: boolean;
}

const ProjectSelector: React.FC<ProjectSelectorProps> = ({ isDarkMode = false }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProjects();
    loadSelectedProject();
  }, []);

  const loadProjects = async () => {
    try {
      const userData = await loginStore.getUserData();
      if (!userData) {
        setError('User data not available');
        return;
      }

      setIsLoading(true);
      setError(null);

      const response = await fetch(`https://devapi.drcode.ai/testgpt/api/projects/user/${userData.user_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setProjects(data.data);
        await loginStore.setProjects(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch projects');
      }
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch projects');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSelectedProject = async () => {
    try {
      const projectId = await loginStore.getSelectedProjectId();
      setSelectedProjectId(projectId);
    } catch (error) {
      console.error('Error loading selected project:', error);
    }
  };

  const handleProjectSelect = async (projectId: number) => {
    try {
      setSelectedProjectId(projectId);
      await loginStore.setSelectedProject(projectId);
    } catch (error) {
      console.error('Error setting selected project:', error);
      setError('Failed to save project selection');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <FiFolder className={`w-4 h-4 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Projects</h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-3">
            <FiLoader className={`w-5 h-5 animate-spin ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-500'}`} />
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Loading projects...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <FiFolder className={`w-4 h-4 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Projects</h3>
        </div>
        <div
          className={`p-4 rounded-lg border ${
            isDarkMode ? 'border-red-600/30 text-red-400' : 'border-red-200 text-red-600'
          }`}>
          <p className="text-sm mb-3">{error}</p>
          <button
            onClick={loadProjects}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 ${
              isDarkMode ? 'bg-[#875bf8] text-white hover:bg-[#a478f9]' : 'bg-sky-500 text-white hover:bg-sky-600'
            }`}>
            <FiRefreshCw className="w-4 h-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <FiFolder className={`w-4 h-4 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Projects</h3>
        </div>
        <div className="text-center py-8">
          <p className={`mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>No projects found</p>
          <button
            onClick={loadProjects}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 mx-auto ${
              isDarkMode ? 'bg-[#875bf8] text-white hover:bg-[#a478f9]' : 'bg-sky-500 text-white hover:bg-sky-600'
            }`}>
            <FiRefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <FiFolder className={`w-4 h-4 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`} />
        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Select Project</h3>
      </div>

      <div className="space-y-2">
        {projects.map(project => (
          <button
            key={project.id}
            className={`w-full p-4 rounded-lg border text-left transition-all duration-200 hover:scale-[1.02] ${
              selectedProjectId === project.id
                ? isDarkMode
                  ? 'border-[#875bf8] bg-[#875bf8]/10'
                  : 'border-sky-500 bg-sky-50'
                : isDarkMode
                  ? 'border-[#2e2e60] hover:border-[#875bf8]/50'
                  : 'border-sky-200 hover:border-sky-400'
            }`}
            onClick={() => handleProjectSelect(project.id)}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleProjectSelect(project.id);
              }
            }}>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className={`font-semibold text-base mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  {project.project_name}
                </div>
                <div
                  className={`flex items-center space-x-3 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <span>ID: {project.id}</span>
                  {project.env_id && <span>Env: {project.env_id}</span>}
                </div>
              </div>
              {selectedProjectId === project.id && (
                <FiCheck className={`w-5 h-5 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-500'}`} />
              )}
            </div>
          </button>
        ))}
      </div>

      {selectedProjectId && (
        <div
          className={`p-3 rounded-lg border text-center ${
            isDarkMode ? 'border-[#2e2e60] text-gray-300' : 'border-sky-200 text-gray-600'
          }`}>
          <p className="text-sm">
            Selected Project ID: <span className="font-semibold">{selectedProjectId}</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default ProjectSelector;
